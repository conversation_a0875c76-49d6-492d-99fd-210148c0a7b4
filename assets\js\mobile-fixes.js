/**
 * Mobile Fixes for Button Clicks and Navigation
 * Ensures all interactive elements work properly on mobile devices
 */

document.addEventListener('DOMContentLoaded', function() {
    if (window.innerWidth <= 768) {
        initializeMobileFixes();

        // Ensure Feather icons are initialized for mobile
        setTimeout(() => {
            if (typeof feather !== 'undefined') {
                feather.replace();
            }
        }, 150);
    }
});

function initializeMobileFixes() {
    fixMobileNavigation();
    fixButtonClicks();
    fixModalTriggers();
    fixFormSubmissions();
}

/**
 * Fix mobile navigation button clicks
 */
function fixMobileNavigation() {
    // Fix login button in mobile nav - ensure it works with the popup system
    const loginButtons = document.querySelectorAll('#login-button, .login-btn');

    loginButtons.forEach(button => {
        // Remove any existing event listeners to prevent duplicates
        button.removeEventListener('click', handleLoginClick);
        button.addEventListener('click', handleLoginClick);
    });

    // Fix theme toggle in mobile nav - ensure it works properly
    const themeToggle = document.querySelector('#mobile-theme-toggle');
    if (themeToggle) {
        // Remove any existing event listeners to prevent duplicates
        themeToggle.removeEventListener('click', handleThemeToggle);
        themeToggle.addEventListener('click', handleThemeToggle);
    }
}

/**
 * Handle login button clicks
 */
function handleLoginClick(e) {
    e.preventDefault();
    e.stopPropagation();

    // Wait a bit for the footer script to create popup elements if they don't exist
    setTimeout(() => {
        const loginPopup = document.getElementById('login-popup');
        const loginOverlay = document.getElementById('login-overlay');

        if (loginPopup && loginOverlay) {
            loginPopup.classList.add('active');
            loginOverlay.classList.add('active');
        } else {
            // Create a simple alert as fallback
            alert('Please use the login form. The popup system is loading...');
            // Or redirect to a login page
            // window.location.href = '/login.php';
        }
    }, 100);
}

/**
 * Handle theme toggle clicks
 */
function handleThemeToggle(e) {
    e.preventDefault();
    e.stopPropagation();

    const body = document.body;
    const isDark = body.classList.contains('dark-theme');

    // Toggle theme
    if (isDark) {
        body.classList.remove('dark-theme');
        document.cookie = 'theme=light; path=/; max-age=31536000';
    } else {
        body.classList.add('dark-theme');
        document.cookie = 'theme=dark; path=/; max-age=31536000';
    }

    // Update mobile theme toggle icon
    const mobileIcon = this.querySelector('i[data-feather]');
    if (mobileIcon) {
        mobileIcon.setAttribute('data-feather', isDark ? 'moon' : 'sun');
        // Force re-render of Feather icons
        setTimeout(() => {
            if (typeof feather !== 'undefined') {
                feather.replace();
            }
        }, 50);
    }

    // Update desktop theme toggle icon if it exists
    const desktopThemeToggle = document.getElementById('theme-toggle');
    if (desktopThemeToggle) {
        const desktopIcon = desktopThemeToggle.querySelector('i');
        if (desktopIcon) {
            if (isDark) {
                desktopIcon.classList.replace('fa-sun', 'fa-moon');
            } else {
                desktopIcon.classList.replace('fa-moon', 'fa-sun');
            }
        }
    }
}

/**
 * Fix general button clicks
 */
function fixButtonClicks() {
    // Fix all buttons with onclick attributes
    const buttonsWithOnclick = document.querySelectorAll('[onclick]');
    
    buttonsWithOnclick.forEach(button => {
        const originalOnclick = button.getAttribute('onclick');
        
        // Add touch-friendly click handler
        button.addEventListener('click', function(e) {
            // For regular links, let them work normally
            if (button.tagName === 'A' && button.getAttribute('href') && 
                !button.getAttribute('href').startsWith('#') && 
                !originalOnclick.includes('preventDefault')) {
                return;
            }
            
            e.preventDefault();
            e.stopPropagation();
            
            try {
                // Execute the original onclick function
                const func = new Function(originalOnclick);
                func.call(button);
            } catch (error) {
                console.error('Error executing onclick function:', error);
                console.log('Original onclick:', originalOnclick);
            }
        });
        
        // Add touch feedback
        button.addEventListener('touchstart', function() {
            this.style.opacity = '0.7';
        });
        
        button.addEventListener('touchend', function() {
            this.style.opacity = '';
        });
        
        button.addEventListener('touchcancel', function() {
            this.style.opacity = '';
        });
    });
}

/**
 * Fix modal triggers
 */
function fixModalTriggers() {
    // Fix modal trigger buttons
    const modalTriggers = document.querySelectorAll('[data-toggle="modal"], [data-target*="modal"], .modal-trigger');
    
    modalTriggers.forEach(trigger => {
        trigger.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const targetModal = this.getAttribute('data-target') || this.getAttribute('href');
            if (targetModal) {
                const modal = document.querySelector(targetModal);
                if (modal) {
                    modal.style.display = 'flex';
                    modal.classList.add('show');
                    document.body.style.overflow = 'hidden';
                }
            }
        });
    });
    
    // Fix modal close buttons
    const modalCloseButtons = document.querySelectorAll('.modal-close, .close, [data-dismiss="modal"]');
    
    modalCloseButtons.forEach(closeBtn => {
        closeBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const modal = this.closest('.modal, .popup-overlay');
            if (modal) {
                modal.style.display = 'none';
                modal.classList.remove('show');
                document.body.style.overflow = '';
            }
        });
    });
}

/**
 * Fix form submissions
 */
function fixFormSubmissions() {
    // Ensure form submit buttons work
    const submitButtons = document.querySelectorAll('input[type="submit"], button[type="submit"], .submit-btn');
    
    submitButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            const form = this.closest('form');
            if (form) {
                // Let the form handle the submission naturally
                return;
            }
        });
    });
}

/**
 * Add touch feedback to all interactive elements
 */
function addTouchFeedback() {
    const interactiveElements = document.querySelectorAll('button, .btn, a, input[type="submit"], input[type="button"]');
    
    interactiveElements.forEach(element => {
        element.addEventListener('touchstart', function() {
            this.classList.add('touch-active');
        });
        
        element.addEventListener('touchend', function() {
            this.classList.remove('touch-active');
        });
        
        element.addEventListener('touchcancel', function() {
            this.classList.remove('touch-active');
        });
    });
}

// Initialize touch feedback and ensure mobile navigation works
document.addEventListener('DOMContentLoaded', function() {
    if (window.innerWidth <= 768) {
        addTouchFeedback();

        // Additional mobile navigation initialization
        setTimeout(initializeMobileNavigation, 300);
    }
});

/**
 * Initialize mobile navigation with proper icon rendering
 */
function initializeMobileNavigation() {
    // Re-initialize mobile fixes to ensure event listeners are attached
    initializeMobileFixes();

    // Ensure all Feather icons are rendered with retry logic
    let attempts = 0;
    const maxAttempts = 3;

    function tryRenderIcons() {
        if (typeof feather !== 'undefined') {
            feather.replace();
        } else if (attempts < maxAttempts) {
            attempts++;
            setTimeout(tryRenderIcons, 200 * attempts);
        }
    }

    tryRenderIcons();
}

// Re-initialize on window resize
window.addEventListener('resize', function() {
    if (window.innerWidth <= 768) {
        setTimeout(initializeMobileFixes, 100);
    }
});

// Add CSS for touch feedback
const style = document.createElement('style');
style.textContent = `
    .touch-active {
        opacity: 0.7 !important;
        transform: scale(0.98) !important;
        transition: all 0.1s ease !important;
    }
    
    @media (max-width: 768px) {
        button, .btn, a[href] {
            -webkit-tap-highlight-color: rgba(0,0,0,0.1);
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            user-select: none;
        }
        
        /* Ensure buttons are touch-friendly */
        button, .btn {
            min-height: 44px;
            min-width: 44px;
            cursor: pointer;
            touch-action: manipulation;
        }
    }
`;
document.head.appendChild(style);
